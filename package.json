{"name": "adopt-ui", "version": "0.0.1", "scripts": {"ng": "ng", "prettier": "prettier --write \"**/*.{js,json,css,scss,less,md,ts,html,component.html}\"", "start": "ng serve --port 4300", "build": "ng build --prod --vendor-chunk --common-chunk --delete-output-path", "watch": "ng build --watch --configuration development", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@agm/core": "^3.0.0-beta.0", "@angular/animations": "~11.2.7", "@angular/cdk": "^11.2.13", "@angular/common": "^11.2.14", "@angular/compiler": "~11.2.7", "@angular/core": "~11.2.7", "@angular/forms": "~11.2.7", "@angular/material": "^11.2.8", "@angular/material-moment-adapter": "^11.2.12", "@angular/platform-browser": "~11.2.7", "@angular/platform-browser-dynamic": "~11.2.7", "@angular/router": "~11.2.7", "@binssoft/ngx-captcha": "^1.3.0", "@dabeng/ng-orgchart": "^1.0.2", "@fullcalendar/angular": "^5.11.5", "@fullcalendar/core": "^5.11.5", "@fullcalendar/daygrid": "^5.11.5", "@fullcalendar/interaction": "^5.11.5", "@fullcalendar/timegrid": "^5.11.5", "@mdi/angular-material": "^3.9.97", "@ng-bootstrap/ng-bootstrap": "^9.1.1", "@ng-select/ng-select": "^6.1.0", "@types/file-saver": "^2.0.4", "address": "^1.1.2", "angular-confirmation-popover": "^6.0.0", "angular-org-chart": "^1.0.3", "bootstrap": "^5.0.2", "chart.js": "^2.9.4", "crypto-js": "^4.2.0", "file-saver": "^2.0.5", "husky": "^4.3.8", "jspdf": "^2.5.1", "jspdf-autotable": "^3.5.23", "jwt-decode": "^3.1.2", "keycloak-js": "^11.0.3", "moment": "^2.29.3", "ng2-search-filter": "^0.5.1", "ngx-google-places-autocomplete": "^2.0.5", "ngx-pagination": "^5.0.0", "ngx-spinner": "^11.0.2", "panzoom": "^9.4.3", "primeflex": "^2.0.0", "primeicons": "^4.1.0", "primeng": "^11.4.3", "rxjs": "~6.6.0", "svg-country-flags": "^1.2.10", "tslib": "^2.3.1", "underscore": "^1.13.0", "xlsx": "^0.17.1", "zone.js": "~0.11.3"}, "devDependencies": {"@angular-devkit/build-angular": "^0.1102.8", "@angular/cli": "^11.2.14", "@angular/compiler-cli": "~11.2.7", "@schuchard/prettier": "^5.1.0", "@types/googlemaps": "3.39.13", "@types/jasmine": "~3.6.0", "@types/node": "^12.20.36", "codelyzer": "^6.0.0", "jasmine-core": "~3.7.1", "jasmine-spec-reporter": "~5.0.0", "karma": "^6.3.20", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.0.3", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.6.0", "lint-staged": "latest", "prettier": "latest", "protractor": "~7.0.0", "ts-node": "~8.3.0", "tslint": "~6.1.0", "tslint-config-prettier": "latest", "typescript": "~4.1.5"}, "overrides": {"stylus": "0.0.1-security"}}